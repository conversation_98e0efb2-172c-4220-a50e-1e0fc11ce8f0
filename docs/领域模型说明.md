# 领域模型说明

## 领域模型概述
本项目围绕"门店巡检"业务，核心领域模型包括：检查表、巡检报告、整改任务、人员、模板等。各模型间通过ID、外键等方式建立关联，支撑巡检任务的全流程管理。

## 核心实体关系图
```mermaid
classDiagram
  class StoreInspectionTemplate {
    +Long id
    +String title
    +String description
    +Boolean canUse
    +LocalDateTime createTime
    +String createPerson
  }
  
  class StoreInspectionTemplateDetail {
    +Long id
    +Long templateId
    +String checkCategoryId
    +String checkCategoryTitle
    +String checkDetailId
    +String checkDetailTitle
    +String checkDetailStandard
    +Integer score
  }
  
  class StoreInspectionChecklist {
    +Long id
    +String title
    +String description
    +Boolean canUse
    +Long storePkgId
    +String feedbackMethod
    +Long templateId
    +LocalDateTime createTime
    +String createPerson
  }
  
  class StoreInspectionPerson {
    +Long id
    +Long checklistId
    +Long userId
    +String userName
    +LocalDateTime createTime
  }
  
  class StoreInspectionReport {
    +Long id
    +String title
    +Long storeId
    +String storeCode
    +String storeName
    +BigDecimal totalScore
    +BigDecimal totalGainScore
    +BigDecimal gainScoreRate
    +LocalDateTime deadline
    +String summary
    +Long checklistId
    +String checklistTitle
    +Long userId
    +String feedbackMethod
  }
  
  class StoreInspectionReportDetail {
    +Long id
    +Long reportId
    +String checkCategoryId
    +String checkCategoryTitle
    +String checkDetailId
    +String checkDetailTitle
    +String checkDetailStandard
    +Integer score
    +Integer gainScore
    +Boolean canAdjust
    +String adjustContent
    +String picList
  }
  
  class StoreInspectionAdjustTask {
    +Long id
    +Long reportId
    +Long storeId
    +String storeCode
    +String storeName
    +String checkCategoryTitle
    +String checkDetailTitle
    +String adjustContent
    +Integer status
    +LocalDateTime deadline
    +String checkCategoryId
    +String checkDetailId
    +Long userId
    +String feedbackMethod
  }
  
  class StoreInspectionAdjustTaskRecord {
    +Long id
    +Long adjustTaskId
    +Integer type
    +String picList
    +String description
    +Boolean result
    +Integer fromStatus
    +Integer toStatus
  }

  StoreInspectionTemplate "1" -- "n" StoreInspectionTemplateDetail : templateId
  StoreInspectionTemplate "1" -- "n" StoreInspectionChecklist : templateId
  StoreInspectionChecklist "1" -- "n" StoreInspectionPerson : checklistId
  StoreInspectionChecklist "1" -- "n" StoreInspectionReport : checklistId
  StoreInspectionReport "1" -- "n" StoreInspectionReportDetail : reportId
  StoreInspectionReport "1" -- "n" StoreInspectionAdjustTask : reportId
  StoreInspectionAdjustTask "1" -- "n" StoreInspectionAdjustTaskRecord : adjustTaskId
```

## 实体属性详细说明

### StoreInspectionTemplate（巡检模板）
| 属性名 | 类型 | 说明 | 业务规则 |
|----|---|---|---|
| id | Long | 自增主键 | 系统生成 |
| title | String | 模板名称 | 必填，长度限制2-100字符 |
| description | String | 模板描述 | 可选，最大500字符 |
| canUse | Boolean | 是否启用 | true=启用，false=禁用 |
| createTime | LocalDateTime | 创建时间 | 系统自动生成 |
| createPerson | String | 创建人 | 必填 |

**业务规则**：
- 模板启用后才能被检查表引用
- 删除模板前需确保没有关联的检查表

### StoreInspectionTemplateDetail（模板明细）
| 属性名 | 类型 | 说明 | 业务规则 |
|----|---|---|---|
| templateId | Long | 模板ID | 外键关联 |
| checkCategoryId | String | 分类ID | 检查分类标识 |
| checkCategoryTitle | String | 分类名称 | 检查分类显示名称 |
| checkDetailId | String | 检查项ID | 检查项标识 |
| checkDetailTitle | String | 检查项名称 | 检查项显示名称 |
| checkDetailStandard | String | 检查标准 | 检查项的评判标准 |
| score | Integer | 分值 | 该检查项的满分值 |

### StoreInspectionChecklist（巡店检查表）
| 属性名 | 类型 | 说明 | 业务规则 |
|----|---|---|---|
| title | String | 检查表名称 | 必填，长度限制2-100字符 |
| storePkgId | Long | 门店包ID | 关联门店范围 |
| feedbackMethod | String | 问题反馈方式 | 枚举值：微信群、钉钉群等 |
| templateId | Long | 巡查模板ID | 外键关联，模板快照 |

**业务规则**：
- 检查表创建时会快照模板内容到模板明细表
- 启用状态的检查表才能被用于巡检

### StoreInspectionReport（巡店报告）
| 属性名 | 类型 | 说明 | 业务规则 |
|----|---|---|---|
| title | String | 报告标题 | 格式：yyyyMMdd-门店编号-随机码 |
| totalScore | BigDecimal | 总分值 | 所有检查项分值之和 |
| totalGainScore | BigDecimal | 总得分 | 实际获得分数 |
| gainScoreRate | BigDecimal | 得分率 | 得分率 = 总得分/总分值 |
| deadline | LocalDateTime | 整改截止日期 | 系统计算生成 |

**状态流转**：报告一旦提交不可修改，只能查看

### StoreInspectionAdjustTask（整改任务）
| 属性名 | 类型 | 说明 | 业务规则 |
|----|---|---|---|
| status | Integer | 整改任务状态 | 见状态枚举 |
| adjustContent | String | 整改内容 | 问题描述和整改要求 |

**状态枚举**：
- 1: 待反馈
- 2: 待审核  
- 3: 待整改
- 4: 已完成
- 5: 超时待反馈
- 6: 超时待审核
- 7: 超时待整改
- 8: 已关闭

**状态转换规则**：
```mermaid
stateDiagram-v2
    [*] --> 待反馈: 创建任务
    待反馈 --> 待审核: 门店反馈
    待审核 --> 待整改: 审核不通过
    待审核 --> 已完成: 审核通过
    待整改 --> 待反馈: 整改完成
    待反馈 --> 超时待反馈: 超时
    待审核 --> 超时待审核: 超时
    待整改 --> 超时待整改: 超时
    超时待反馈 --> 待审核: 门店反馈
    超时待审核 --> 待整改: 审核不通过
    超时待审核 --> 已完成: 审核通过
    超时待整改 --> 待反馈: 整改完成
    任意状态 --> 已关闭: 检查员关闭
```

## 关键业务场景下的模型交互

### 巡检报告提交流程
1. **检查员选择检查表** → 加载模板快照数据
2. **填写检查结果** → 生成报告和报告明细
3. **自动创建整改任务** → 针对需要整改的检查项
4. **计算得分率** → 更新报告统计信息

### 整改任务处理流程
1. **门店人员反馈** → 创建反馈记录，状态变更为"待审核"
2. **检查员审核** → 创建审核记录，根据审核结果变更状态
3. **状态流转** → 使用乐观锁确保状态变更的原子性

## 数据流转关系
- **模板 → 检查表**：模板作为检查表的基础配置
- **检查表 → 报告**：检查表实例化为具体的巡检报告
- **报告 → 整改任务**：报告中的问题项自动生成整改任务
- **整改任务 → 处理记录**：任务处理过程的完整记录
