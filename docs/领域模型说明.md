# 领域模型说明

## 领域模型概述
本项目围绕"门店巡检"业务，核心领域模型包括：检查表、巡检报告、整改任务、人员、模板等。各模型间通过ID、外键等方式建立关联，支撑巡检任务的全流程管理。

## 核心实体关系图
```mermaid
classDiagram
  class SgChecklist {
    Long id
    String name
    String content
    Integer status
    Integer isDel
    String createBy
    Date createTime
    Date updateTime
    String linkUrl
  }
  class StoreInspectionChecklist {
    Long id
    LocalDateTime createTime
    String createPerson
    LocalDateTime updateTime
    String updatePerson
    Boolean isDelete
    String title
    String description
    Boolean canUse
    Long storePkgId
    String feedbackMethod
    Long templateId
  }
  class StoreInspectionReport {
    Long id
    LocalDateTime createTime
    String createPerson
    LocalDateTime updateTime
    String updatePerson
    Boolean isDelete
    String title
    Long storeId
    String storeCode
    String storeName
    BigDecimal totalScore
    BigDecimal totalGainScore
    BigDecimal gainScoreRate
    LocalDateTime deadline
    String summary
    Long checklistId
    String checklistTitle
    Long userId
    String feedbackMethod
  }
  class StoreInspectionAdjustTask {
    Long id
    LocalDateTime createTime
    String createPerson
    LocalDateTime updateTime
    String updatePerson
    Boolean isDelete
    Long reportId
    Long storeId
    String storeCode
    String storeName
    String checkCategoryTitle
    String checkDetailTitle
    String adjustContent
    Integer status
    LocalDateTime deadline
    String checkCategoryId
    String checkDetailId
    Long userId
    String feedbackMethod
  }
  class StoreInspectionPerson {
    Long id
    LocalDateTime createTime
    String createPerson
    LocalDateTime updateTime
    String updatePerson
    Boolean isDelete
    Long checklistId
    Long userId
    String userName
    String outId
  }
  class StoreInspectionTemplate {
    Long id
    LocalDateTime createTime
    String createPerson
    LocalDateTime updateTime
    String updatePerson
    Boolean isDelete
    String title
    String description
    Boolean canUse
  }
  StoreInspectionReport "n" -- "1" StoreInspectionChecklist : checklistId
  StoreInspectionAdjustTask "n" -- "1" StoreInspectionReport : reportId
  StoreInspectionPerson "n" -- "1" StoreInspectionChecklist : checklistId
  StoreInspectionChecklist "n" -- "1" StoreInspectionTemplate : templateId
```

## 实体属性详细说明

### SgChecklist（检查表信息）
| 属性名 | 类型 | 说明 |
|----|---|---|
| id | Long | 主键ID |
| name | String | 检查表名称 |
| content | String | 描述内容 |
| status | Integer | 0禁用 1启用 |
| isDel | Integer | 0正常 1删除 |
| createBy | String | 创建人 |
| createTime | Date | 创建时间 |
| updateTime | Date | 更新时间 |
| linkUrl | String | 链接 |

### StoreInspectionChecklist（巡店检查表）
| 属性名 | 类型 | 说明 |
|----|---|---|
| id | Long | 自增主键 |
| createTime | LocalDateTime | 创建时间 |
| createPerson | String | 创建人 |
| updateTime | LocalDateTime | 修改时间 |
| updatePerson | String | 修改人 |
| isDelete | Boolean | 是否删除 |
| title | String | 检查表名称 |
| description | String | 备注 |
| canUse | Boolean | 是否启用 |
| storePkgId | Long | 门店包id |
| feedbackMethod | String | 问题反馈方式 |
| templateId | Long | 巡查模板id |

### StoreInspectionReport（巡店报告表）
| 属性名 | 类型 | 说明 |
|----|---|---|
| id | Long | 自增主键 |
| createTime | LocalDateTime | 创建时间 |
| createPerson | String | 创建人 |
| updateTime | LocalDateTime | 修改时间 |
| updatePerson | String | 修改人 |
| isDelete | Boolean | 是否删除 |
| title | String | 报告标题 |
| storeId | Long | 门店id |
| storeCode | String | 门店编号 |
| storeName | String | 门店名称 |
| totalScore | BigDecimal | 总分值 |
| totalGainScore | BigDecimal | 总得分 |
| gainScoreRate | BigDecimal | 得分率 |
| deadline | LocalDateTime | 整改截止日期 |
| summary | String | 报告总结 |
| checklistId | Long | 巡店检查表id |
| checklistTitle | String | 巡店检查表名称 |
| userId | Long | 提交人id |
| feedbackMethod | String | 问题反馈方式 |

### StoreInspectionAdjustTask（整改任务表）
| 属性名 | 类型 | 说明 |
|----|---|---|
| id | Long | 自增主键 |
| createTime | LocalDateTime | 创建时间 |
| createPerson | String | 创建人 |
| updateTime | LocalDateTime | 修改时间 |
| updatePerson | String | 修改人 |
| isDelete | Boolean | 是否删除 |
| reportId | Long | 报告id |
| storeId | Long | 门店id |
| storeCode | String | 门店编号 |
| storeName | String | 门店名称 |
| checkCategoryTitle | String | 分类名称 |
| checkDetailTitle | String | 检查项名称 |
| adjustContent | String | 整改内容 |
| status | Integer | 整改任务状态（枚举，含状态流转规则） |
| deadline | LocalDateTime | 整改截止日期 |
| checkCategoryId | String | 分类id |
| checkDetailId | String | 检查项id |
| userId | Long | 下发人id |
| feedbackMethod | String | 问题反馈方式 |

### StoreInspectionPerson（巡店人员表）
| 属性名 | 类型 | 说明 |
|----|---|---|
| id | Long | 自增主键 |
| createTime | LocalDateTime | 创建时间 |
| createPerson | String | 创建人 |
| updateTime | LocalDateTime | 修改时间 |
| updatePerson | String | 修改人 |
| isDelete | Boolean | 是否删除 |
| checklistId | Long | 巡店检查表id |
| userId | Long | 关联用户id |
| userName | String | 用户名称 |
| outId | String | 外部表id |

### StoreInspectionTemplate（巡店检查模板表）
| 属性名 | 类型 | 说明 |
|----|---|---|
| id | Long | 自增主键 |
| createTime | LocalDateTime | 创建时间 |
| createPerson | String | 创建人 |
| updateTime | LocalDateTime | 修改时间 |
| updatePerson | String | 修改人 |
| isDelete | Boolean | 是否删除 |
| title | String | 模板名称 |
| description | String | 备注 |
| canUse | Boolean | 是否启用 |

## 业务规则与状态流转（以整改任务为例）

- 整改任务状态（TaskStatusEnum）：
  - WAIT_FEEDBACK（待反馈）
  - WAIT_AUDIT（待审核）
  - WAIT_ADJUST（待整改）
  - FINISHED（已完成）
  - TIMEOUT_WAIT_FEEDBACK（超时待反馈）
  - TIMEOUT_WAIT_AUDIT（超时待审核）
  - TIMEOUT_WAIT_ADJUST（超时待整改）
  - CLOSED（已关闭）

- 状态流转规则（部分）：
  - 待反馈 → 待审核/超时待反馈/已关闭
  - 待审核 → 待整改/已完成/超时待审核/已关闭
  - 待整改 → 待审核/超时待整改/已关闭
  - 已完成、已关闭、超时状态不可再变更 