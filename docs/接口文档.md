# 接口文档

## 检查表管理（SiChecklistAdminController）

| 接口名称 | 路径 | 方法 | 入参 | 出参 | 说明 | 代码位置 |
|---|---|---|---|---|---|---|
| 获取启用的模板列表 | /admin/si/checklist/templates | GET | 无 | List<SiTemplateResp> | 获取启用的模板 | jic-store-inspection-center/src/main/java/org/springcenter/store/api/SiChecklistAdminController.java |
| 保存检查表 | /admin/si/checklist/save | POST | SiChecklistSaveReq | Long | 新增检查表 | 同上 |
| 查看检查表详情 | /admin/si/checklist/detail/{id} | GET | id | SiChecklistResp | 获取详情 | 同上 |
| 编辑检查表 | /admin/si/checklist/update/{id} | POST | id, SiChecklistSaveReq | Long | 编辑检查表 | 同上 |
| 启用检查表 | /admin/si/checklist/enable | POST | SiEnableReq | Void | 启用 | 同上 |
| 禁用检查表 | /admin/si/checklist/disable | POST | SiEnableReq | Void | 禁用 | 同上 |
| 查询检查表列表 | /admin/si/checklist/list | POST | CommonRequest<SiChecklistListReq> | List<SiChecklistResp> | 分页查询 | 同上 |

## 检查模板管理（SiTemplateAdminController）

| 接口名称 | 路径 | 方法 | 入参 | 出参 | 说明 | 代码位置 |
|---|---|---|---|---|---|---|
| 保存模板 | /admin/si/template/save | POST | SiTemplateSaveReq | Long | 新增模板 | jic-store-inspection-center/src/main/java/org/springcenter/store/api/SiTemplateAdminController.java |
| 查看模板详情 | /admin/si/template/detail/{id} | GET | id | SiTemplateResp | 获取详情 | 同上 |
| 编辑模板 | /admin/si/template/update/{id} | POST | id, SiTemplateSaveReq | Long | 编辑模板 | 同上 |
| 查询模板列表 | /admin/si/template/list | POST | CommonRequest<SiTemplateListReq> | List<SiTemplateResp> | 分页查询 | 同上 |
| 启用模板 | /admin/si/template/enable | POST | SiEnableReq | Void | 启用 | 同上 |
| 禁用模板 | /admin/si/template/disable | POST | SiEnableReq | Void | 禁用 | 同上 |

## 巡店报告管理（SiReportAdminController）

| 接口名称 | 路径 | 方法 | 入参 | 出参 | 说明 | 代码位置 |
|---|---|---|---|---|---|---|
| 查询巡店报告列表 | /admin/si/report/list | POST | CommonRequest<SiReportAdminListReq> | List<SiReportListResp> | 分页查询 | jic-store-inspection-center/src/main/java/org/springcenter/store/api/SiReportAdminController.java |
| 查看巡店报告详情 | /admin/si/report/detail/{id} | GET | id | SiReportDetailResp | 获取详情 | 同上 |

## 整改任务管理（SiAdjustTaskAdminController）

| 接口名称 | 路径 | 方法 | 入参 | 出参 | 说明 | 代码位置 |
|---|---|---|---|---|---|---|
| 查询整改任务列表 | /admin/si/adjust/task/list | POST | CommonRequest<SiTaskAdminListReq> | List<SiTaskAdminListResp> | 分页查询 | jic-store-inspection-center/src/main/java/org/springcenter/store/api/SiAdjustTaskAdminController.java |
| 查询整改处理明细列表 | /admin/si/adjust/task/listTaskRecords | POST | CommonRequest<SiTaskRecordListReq> | List<SiTaskRecordResp> | 分页查询 | 同上 |
| 提交审核 | /admin/si/adjust/task/auditTask | POST | SiTaskAuditReq | Long | 审核 | 同上 |

## 老版检查表管理（SgChecklistAdminController）

| 接口名称 | 路径 | 方法 | 入参 | 出参 | 说明 | 代码位置 |
|---|---|---|---|---|---|---|
| 检查表列表 | /admin/sgChecklist/list | POST | CommonRequest<ChecklistReq> | List<SgChecklistResp> | 分页查询 | jic-store-inspection-center/src/main/java/org/springcenter/store/api/SgChecklistAdminController.java |
| 创建或更新检查表 | /admin/sgChecklist/createOrUpdate | POST | CommonRequest<SgChecklistResp> | 无 | 新增/编辑 | 同上 | 