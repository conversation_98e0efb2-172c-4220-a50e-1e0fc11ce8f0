# 项目结构说明

## 1. 项目模块划分
本项目为单体主模块（jic-store-inspection-center），采用Spring Boot + Spring Cloud微服务架构，支持服务注册、远程调用、分布式任务调度等能力。

- 主模块：jic-store-inspection-center

## 2. 代码组织结构

```
jic-store-inspection-center/
  └─ src/
      └─ main/
          ├─ java/
          │   └─ org/
          │       └─ springcenter/
          │           └─ store/
          │               ├─ api/         # 对外接口（Controller）
          │               ├─ config/      # 配置类（数据库、Redis、Swagger、异常处理等）
          │               ├─ cons/        # 常量
          │               ├─ convert/     # 对象转换
          │               ├─ dto/         # 数据传输对象（DTO/VO）
          │               ├─ entity/      # 领域实体
          │               ├─ job/         # 定时任务
          │               ├─ mapper/      # MyBatis-Plus Mapper接口
          │               ├─ remote/      # 远程服务与API
          │               ├─ service/     # 服务层（含impl实现）
          │               └─ utils/       # 工具类
          └─ resources/
              ├─ application.yml          # 主配置文件
              ├─ bootstrap-*.yml          # 多环境配置
              ├─ mapper/                  # MyBatis XML映射
              └─ logback.xml              # 日志配置
```

## 3. 关键包说明
| 包路径 | 说明 |
|---|---|
| api/ | REST接口层，定义对外API |
| config/ | 各类配置（数据库、缓存、异常、Swagger等） |
| cons/ | 常量定义 |
| convert/ | DTO/Entity等对象转换 |
| dto/ | 业务数据传输对象（请求/响应VO/DTO） |
| entity/ | 领域实体，数据库表映射 |
| job/ | 定时任务与调度 |
| mapper/ | MyBatis-Plus持久层接口 |
| remote/ | 远程服务调用与下游API |
| service/ | 业务服务层，含接口与实现 |
| utils/ | 工具类 |

## 4. 分层架构说明
- 表现层（api/）：负责接收请求、参数校验、返回标准响应
- 服务层（service/）：封装业务逻辑，协调各类资源
- 持久层（mapper/、entity/）：数据库操作与实体映射
- 配置与基础设施层（config/、job/、remote/）：系统配置、定时任务、远程服务 