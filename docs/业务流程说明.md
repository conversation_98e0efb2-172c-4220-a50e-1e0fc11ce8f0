# 业务流程说明

## 检查表创建与维护流程

### 核心流程图
```mermaid
flowchart TD
    A[发起保存检查表请求] --> B{校验名称唯一性}
    B -- 不唯一 --> C[返回"检查表名称已存在"异常]
    B -- 唯一 --> D[保存检查表主数据]
    D --> E[保存检查人员列表]
    E --> F[返回检查表ID]
```

### 调用链路
**入口点**：`SiChecklistAdminController.saveChecklist()`

**调用流程**：
1. 参数校验（@Validated SiChecklistSaveReq）
2. 检查表名称唯一性校验（`StoreInspectionChecklistServiceImpl.saveChecklist`）
3. 保存检查表主数据（`checklistMapper.insert`）
4. 保存检查人员（`savePerson` → `personService.saveBatch`）
5. 事务提交，返回新建检查表ID

### 关键判断点
| 判断点 | 条件 | 处理路径 |
|-----|---|----|
| 名称唯一性 | 存在同名未删除检查表 | 抛出BizException |
| 检查表存在性 | 编辑时未找到对应ID | 抛出BizException |

---

## 检查表编辑流程

### 核心流程图
```mermaid
flowchart TD
    A[发起编辑检查表请求] --> B{检查表是否存在}
    B -- 否 --> C[返回"检查表不存在"异常]
    B -- 是 --> D{新名称是否唯一}
    D -- 否 --> E[返回"检查表名称已存在"异常]
    D -- 是 --> F[更新检查表主数据]
    F --> G[逻辑删除原检查人员]
    G --> H[保存新检查人员]
    H --> I[返回检查表ID]
```

### 调用链路
**入口点**：`SiChecklistAdminController.updateChecklist()`

**调用流程**：
1. 检查表存在性校验
2. 名称唯一性校验（如有变更）
3. 更新主数据（`checklistMapper.updateById`）
4. 逻辑删除原检查人员（`personService.update`）
5. 保存新检查人员（`savePerson`）

---

## 检查表启用/禁用流程

### 核心流程图
```mermaid
flowchart TD
    A[发起启用/禁用请求] --> B[更新canUse字段]
    B --> C[返回成功]
```

**入口点**：`SiChecklistAdminController.enableChecklist()` / `disableChecklist()` 