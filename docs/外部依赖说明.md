# 外部依赖说明

## 下游服务概述

| 服务名称 | 依赖方式 | 主要用途 | 代码位置 |
|---|---|---|---|
| 数据字典服务（jnby-authority-api） | Spring Bean/Feign | 查询数据字典、员工角色等 | jic-store-inspection-center/src/main/java/org/springcenter/store/remote/DictionaryService.java |
| 员工中心服务（IEmployeeHttpApi） | Retrofit2 | 查询员工门店角色 | jic-store-inspection-center/src/main/java/org/springcenter/store/remote/EmployeeService.java |
| 门店中心服务（IStoreCenterHttpApi） | Retrofit2 | 查询门店包、门店信息 | jic-store-inspection-center/src/main/java/org/springcenter/store/remote/StoreService.java |

## 主要外部依赖库

| 依赖名称 | 作用 | 版本 | 代码位置 |
|---|---|---|---|
| Spring Boot | 微服务基础框架 | 2.x | pom.xml |
| Spring Cloud | 微服务注册、配置、调用 | 2.x | pom.xml |
| MyBatis-Plus | ORM框架 | 3.4.1 | pom.xml |
| Redis/Redisson | 缓存、分布式锁 | 3.12.0 | pom.xml |
| RocketMQ | 消息队列 | - | pom.xml |
| XXL-Job | 分布式任务调度 | 2.3.0 | pom.xml |
| Swagger/Swagger-UI | API文档 | 1.9.6 | pom.xml |
| MapStruct | Bean对象转换 | 1.5.5.Final | pom.xml |
| Druid | 数据库连接池 | 1.2.6 | pom.xml |
| Retrofit2 | HTTP客户端 | 2.1.0 | pom.xml |
| PageHelper | 分页插件 | - | pom.xml |
| JUnit | 单元测试 | 4.12 | pom.xml |

## 系统与外部服务调用关系图

```mermaid
flowchart TD
    A[巡店中心服务] -- 查询数据字典 --> B[数据字典服务]
    A -- 查询员工角色 --> C[员工中心服务]
    A -- 查询门店信息 --> D[门店中心服务]
    A -- 消息推送/任务调度 --> E[RocketMQ/XXL-Job]
    A -- 缓存/分布式锁 --> F[Redis/Redisson]
    A -- 数据持久化 --> G[MySQL]
``` 