# 项目概述

## 项目背景
巡店项目（jic-store-inspection）是一个面向零售门店的数字化巡检管理系统，旨在实现门店巡检相关的任务管理、检查表、报告、整改等业务流程的数字化管理。该项目隶属于江南布衣集团的数字化转型战略，通过标准化的巡检流程提升门店运营质量和管理效率。

## 项目目标
- **门店巡检任务的全流程数字化**：从任务创建、分配到执行完成的完整数字化管理
- **检查表、报告、整改等业务的高效协同**：建立标准化的检查流程和整改跟踪机制
- **数据可视化与统计分析**：提供巡检数据的统计分析和可视化展示
- **移动端支持**：支持移动端巡检作业，提升现场操作便利性

## 功能概述
- **巡检任务创建与分配**：支持创建巡检检查表，分配检查人员
- **检查表管理与填写**：提供标准化的检查模板和移动端填写功能
- **检查结果收集与整改跟踪**：自动生成巡检报告，创建整改任务并跟踪处理进度
- **报告生成与统计分析**：生成巡检报告，提供数据统计和分析功能
- **权限管理**：区分检查员和门店人员的不同操作权限

## 技术栈
- **Java 8**：核心开发语言
- **Spring Boot 2.2.12**：微服务基础框架
- **Spring Cloud Hoxton.SR9**：微服务生态（服务注册与发现、Feign远程调用、配置中心）
- **MyBatis-Plus 3.4.1**：ORM框架，简化数据库操作
- **Redis & Redisson 3.12.0**：缓存与分布式锁
- **RocketMQ 4.8.0**：消息队列
- **XXL-Job 2.3.0**：分布式任务调度
- **Swagger 1.9.6**：API文档生成
- **MapStruct 1.5.5.Final**：对象转换映射
- **Druid 1.2.6**：数据库连接池
- **MySQL**：关系型数据库
- **Retrofit2 2.1.0**：HTTP客户端，用于远程服务调用

## 架构类型
**Spring Cloud 微服务架构**

本项目采用Spring Cloud微服务架构，具备以下特点：
- 服务注册与发现（Eureka/Nacos）
- 配置中心管理
- 远程服务调用（Feign）
- 分布式事务支持
- 熔断降级机制

## 是否使用GBF框架
**否**，本项目未使用GBF（Generic Business Framework）框架，而是采用Spring Cloud生态为主的技术栈。项目结构遵循标准的Spring Boot分层架构模式。

## 部署架构
- **容器化部署**：支持Docker容器化部署
- **Kubernetes编排**：提供K8s部署配置文件
- **多环境支持**：支持dev、test、prod多环境配置
- **CI/CD集成**：集成Jenkins自动化部署流水线

## 项目特色
1. **移动端优先**：专为移动端巡检场景设计的API接口
2. **分布式锁保护**：关键业务操作使用Redisson分布式锁防止并发问题
3. **乐观锁机制**：状态变更使用乐观锁确保数据一致性
4. **标准化流程**：建立了完整的巡检-报告-整改闭环流程
5. **灵活的模板系统**：支持自定义检查模板和检查项配置
