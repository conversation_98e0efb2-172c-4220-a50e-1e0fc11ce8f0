# 项目概述

## 项目背景
巡店项目，旨在实现门店巡检相关的任务管理、检查表、报告、整改等业务流程的数字化管理。

## 项目目标
- 门店巡检任务的全流程数字化
- 检查表、报告、整改等业务的高效协同
- 数据可视化与统计分析

## 功能概述
- 巡检任务创建与分配
- 检查表管理与填写
- 检查结果收集与整改跟踪
- 报告生成与统计分析

## 技术栈
- Java 8
- Spring Boot
- Spring Cloud（微服务、服务注册与发现、Feign远程调用）
- MyBatis-Plus（ORM框架）
- Redis、Redisson（缓存与分布式锁）
- RocketMQ（消息队列）
- XXL-Job（分布式任务调度）
- Swagger（API文档）
- MapStruct（对象转换）
- Druid（数据库连接池）
- MySQL
- Retrofit2（HTTP客户端）

## 架构类型
Spring Cloud 微服务架构

## 是否GBF框架
未检测到GBF专有结构，采用Spring Cloud生态为主 