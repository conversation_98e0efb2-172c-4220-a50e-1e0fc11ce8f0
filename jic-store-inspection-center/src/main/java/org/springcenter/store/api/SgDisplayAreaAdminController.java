package org.springcenter.store.api;

import com.jnby.common.CommonRequest;
import com.jnby.common.Page;
import com.jnby.common.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.store.dto.DisplayAreaListReq;
import org.springcenter.store.dto.SgDisplayAreaResp;
import org.springcenter.store.service.SgDisplayAreaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/admin/sgDisplayArea")
@Api(value = "sgDisplayArea", tags = "陈列区域后台接口")
public class SgDisplayAreaAdminController {

    @Autowired
    private SgDisplayAreaService sgDisplayAreaService;


    @ResponseBody
    @PostMapping("/list")
    @ApiOperation(value = "陈列区域列表信息  名称模糊搜索  是否启用搜索")
    public ResponseResult<List<SgDisplayAreaResp>> list(@RequestBody CommonRequest<DisplayAreaListReq> request){
        Page page = request.getPage();
        List<SgDisplayAreaResp> result = sgDisplayAreaService.list(request.getRequestData(),page);
        return ResponseResult.success(result,page);
    }


    @ResponseBody
    @PostMapping("/createOrUpdate")
    @ApiOperation(value = "创建或者编辑")
    public ResponseResult createOrUpdate(@RequestBody CommonRequest<SgDisplayAreaResp> request){
        try {
            sgDisplayAreaService.createOrUpdate(request.getRequestData());
        }catch (Exception e){
            return ResponseResult.error(-1,e.getMessage());
        }
        return ResponseResult.success();
    }



    @ResponseBody
    @PostMapping("/updateIsDelOrStatus")
    @ApiOperation(value = "启用禁用或者删除    is_del  status  id  仅需要这三个字段")
    public ResponseResult updateIsDelOrStatus(@RequestBody CommonRequest<SgDisplayAreaResp> request){
        sgDisplayAreaService.updateIsDelOrStatus(request.getRequestData());
        return ResponseResult.success();
    }

}
