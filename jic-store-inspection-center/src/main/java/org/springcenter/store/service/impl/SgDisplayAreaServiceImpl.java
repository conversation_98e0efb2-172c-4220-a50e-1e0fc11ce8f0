package org.springcenter.store.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jnby.common.Page;
import com.jnby.common.util.IdLeaf;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.product.api.enums.IsDeleteEnum;
import org.springcenter.store.cons.AreaIdConstant;
import org.springcenter.store.dto.DisplayAreaListReq;
import org.springcenter.store.dto.SgDisplayAreaResp;
import org.springcenter.store.dto.SgDisplayAreaScoreResp;
import org.springcenter.store.entity.SgChecklistDisplayRelation;
import org.springcenter.store.entity.SgDisplayArea;
import org.springcenter.store.entity.SgDisplayAreaScore;
import org.springcenter.store.mapper.SgChecklistDisplayRelationMapper;
import org.springcenter.store.mapper.SgDisplayAreaMapper;
import org.springcenter.store.mapper.SgDisplayAreaScoreMapper;
import org.springcenter.store.service.SgDisplayAreaService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service

public class SgDisplayAreaServiceImpl implements SgDisplayAreaService {

    @Autowired
    private SgDisplayAreaMapper sgDisplayAreaMapper;

    @Autowired
    private SgDisplayAreaScoreMapper sgDisplayAreaScoreMapper;

    @Autowired
    private SgChecklistDisplayRelationMapper sgChecklistDisplayRelationMapper;

    @Override
    public List<SgDisplayAreaResp> list(DisplayAreaListReq requestData, Page page) {
        com.github.pagehelper.Page<SgDisplayArea> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        sgDisplayAreaMapper.selectListByReq(requestData);
        PageInfo<SgDisplayArea> pageInfo = new PageInfo<>(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        List<SgDisplayArea> list1 = pageInfo.getList();
        // 封装内层参数
        List<SgDisplayAreaResp> list2 = buildSgDisplayAreaResp(list1);
        return list2;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createOrUpdate(SgDisplayAreaResp sgDisplayAreaResp) {
        // 判断名字不能重复
        QueryWrapper<SgDisplayArea> queryWrapper2 = new QueryWrapper();
        queryWrapper2.eq("name",sgDisplayAreaResp.getName());
        queryWrapper2.eq("is_del",0);
        if(sgDisplayAreaResp.getId() != null){
            queryWrapper2.ne("id",sgDisplayAreaResp.getId());
        }
        List<SgDisplayArea> sgDisplayAreas = sgDisplayAreaMapper.selectList(queryWrapper2);
        if(CollectionUtils.isNotEmpty(sgDisplayAreas)){
            throw new RuntimeException("陈列区域名称不可重复!");
        }


        if(sgDisplayAreaResp.getId() == null){
            // 新增
            SgDisplayArea sgDisplayArea = new SgDisplayArea();
            BeanUtils.copyProperties(sgDisplayAreaResp,sgDisplayArea);
            sgDisplayArea.setCreateTime(new Date());
            sgDisplayArea.setUpdateTime(new Date());
            sgDisplayArea.setIsDel(IsDeleteEnum.NORMAL.getCode());
            sgDisplayAreaMapper.insert(sgDisplayArea);
            sgDisplayAreaResp.setId(sgDisplayArea.getId());
        }else{
            // 编辑
            SgDisplayArea sgDisplayArea = new SgDisplayArea();
            BeanUtils.copyProperties(sgDisplayAreaResp,sgDisplayArea);
            sgDisplayArea.setUpdateTime(new Date());
            sgDisplayAreaMapper.updateById(sgDisplayArea);
        }


        if(CollectionUtils.isNotEmpty(sgDisplayAreaResp.getSgDisplayAreaScoreRespList())){
            // 处理数据  删除之前的
            QueryWrapper<SgDisplayAreaScore> queryWrapper = new QueryWrapper<>();
            SgDisplayAreaScore sgDisplayAreaScore = new SgDisplayAreaScore();
            sgDisplayAreaScore.setSgDisplayAreaId(sgDisplayAreaResp.getId());
            queryWrapper.setEntity(sgDisplayAreaScore);
            sgDisplayAreaScoreMapper.delete(queryWrapper);
            // 新增数据
            for (SgDisplayAreaScoreResp sgDisplayAreaScoreResp : sgDisplayAreaResp.getSgDisplayAreaScoreRespList()) {
                SgDisplayAreaScore sgDisplayAreaScoreInsert = new SgDisplayAreaScore();
                sgDisplayAreaScoreResp.setSgDisplayAreaId(sgDisplayAreaResp.getId());
                BeanUtils.copyProperties(sgDisplayAreaScoreResp,sgDisplayAreaScoreInsert);
//                sgDisplayAreaScoreInsert.setId(IdLeaf.getId(AreaIdConstant.sg_display_area_score));
                sgDisplayAreaScoreInsert.setUpdateTime(new Date());
                sgDisplayAreaScoreInsert.setCreateTime(new Date());
                sgDisplayAreaScoreMapper.insert(sgDisplayAreaScoreInsert);
            }
        }
    }

    @Override
    public void updateIsDelOrStatus(SgDisplayAreaResp sgDisplayAreaResp) {
        SgDisplayArea sgDisplayArea = new SgDisplayArea();
        BeanUtils.copyProperties(sgDisplayAreaResp,sgDisplayArea);
        sgDisplayArea.setUpdateTime(new Date());
        sgDisplayAreaMapper.updateById(sgDisplayArea);

        // 处理数据  如果是删除  或者 下架 禁用
        if(sgDisplayAreaResp.getId() == null){
            return ;
        }
        if((sgDisplayAreaResp.getStatus() != null &&  sgDisplayAreaResp.getStatus().equals(0))
                 ||
                 (sgDisplayAreaResp.getIsDel() != null &&  sgDisplayAreaResp.getIsDel().equals(1))){
            // 下架 或者 禁用  删除那边的数据 软删除
            // 根据 陈列区域id  修改  是否删除
            SgChecklistDisplayRelation sgChecklistDisplayRelation = new SgChecklistDisplayRelation();
            sgChecklistDisplayRelation.setUpdateTime(new Date());
            sgChecklistDisplayRelation.setIsDel(IsDeleteEnum.IS_DELETED.getCode());

            QueryWrapper<SgChecklistDisplayRelation> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("sg_display_area_id",sgDisplayAreaResp.getId());
            sgChecklistDisplayRelationMapper.update(sgChecklistDisplayRelation,queryWrapper);
        }
    }

    public  List<SgDisplayAreaResp> buildSgDisplayAreaResp(List<SgDisplayArea> list) {
        List<SgDisplayAreaResp> resultList = new ArrayList<>();
        // 封装参数给前端
        if(CollectionUtils.isEmpty(list)){
            return new ArrayList<>();
        }
        // 处理数据
        List<Long> ids = list.stream().map(r -> r.getId()).collect(Collectors.toList());
        // 查询数据
        List<SgDisplayAreaScore> sgDisplayAreaScores = sgDisplayAreaScoreMapper.selectByDisplayAreaIds(ids);
        String jsonString1 = JSONObject.toJSONString(sgDisplayAreaScores);
        List<SgDisplayAreaScoreResp> sgDisplayAreaScoreResps = JSONObject.parseArray(jsonString1, SgDisplayAreaScoreResp.class);
        // 处理数据
        Map<Long, List<SgDisplayAreaScoreResp>> groupByDisplayAreaId = sgDisplayAreaScoreResps.stream().collect(Collectors.groupingBy(r -> r.getSgDisplayAreaId()));
        for (SgDisplayArea sgDisplayArea : list) {
            SgDisplayAreaResp sgDisplayAreaResp = new SgDisplayAreaResp();
            BeanUtils.copyProperties(sgDisplayArea,sgDisplayAreaResp);
            sgDisplayAreaResp.setSgDisplayAreaScoreRespList(groupByDisplayAreaId.get(sgDisplayArea.getId()));
            resultList.add(sgDisplayAreaResp);
        }
        return resultList;
    }
}
